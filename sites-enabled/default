# HTTP server - redirect to HTTPS
server {
    listen 80 default_server;
    listen [::]:80 default_server;
    server_name *************;
    
    # Redirect all HTTP requests to HTTPS
    return 301 https://$server_name$request_uri;
}

# HTTPS server with SSL
server {
    listen 443 ssl default_server;
    listen [::]:443 ssl default_server;
    server_name *************;

    # SSL Configuration
    ssl_certificate /etc/nginx/ssl/dhruva.crt;
    ssl_certificate_key /etc/nginx/ssl/dhruva.key;

    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:50m;
    ssl_session_timeout 10m;

    # 🚨 CRITICAL SSL BUFFER SETTINGS FOR LARGE PAYLOADS
    ssl_buffer_size 16k;                    # Increase SSL record size for large payloads

    # 🚨 GLOBAL SETTINGS FOR LARGE REQUEST BODIES OVER SSL
    client_max_body_size 100M;             # Global limit increase
    client_body_buffer_size 5M;            # Large buffer for request bodies
    client_body_timeout 300s;              # Extended timeout for large uploads
    client_header_timeout 60s;             # Header timeout

    # 🚨 LARGE FILES TEMP DIRECTORY FOR SSL REQUESTS
    client_body_temp_path /var/cache/nginx/client_temp 1 2;

    # Frontend - Next.js on port 3001 under /dhruva path
    location /dhruva {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Backend API - FastAPI on port 8000 under /backend path
location /auth/ {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

        # Backend API - FastAPI on port 8000 under /backend path
# location /services/ {
#         proxy_pass http://localhost:8000;
#         client_max_body_size 20M;
#         proxy_http_version 1.1;
#         proxy_set_header Host $host;
#         proxy_set_header X-Real-IP $remote_addr;
#         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#         proxy_set_header X-Forwarded-Proto $scheme;
#     }

    # 🚨 CRITICAL: SSL-OPTIMIZED ASR SERVICES CONFIGURATION
    location /services/ {
        # 🔧 DISABLE REQUEST BUFFERING - CRITICAL FOR SSL
        proxy_request_buffering off;

        # 🔧 SSL-SPECIFIC CLIENT BODY SETTINGS
        client_max_body_size 100M;
        client_body_buffer_size 5M;
        client_body_timeout 300s;
        client_body_in_file_only clean;     # Write large bodies to temp files - CRITICAL FOR SSL

        # 🔧 PROXY BUFFER SETTINGS FOR SSL
        proxy_buffering off;                # Disable response buffering for real-time
        proxy_buffer_size 64k;
        proxy_buffers 8 64k;
        proxy_busy_buffers_size 128k;
        proxy_temp_file_write_size 64k;
        proxy_max_temp_file_size 2048m;

        # 🔧 EXTENDED TIMEOUTS FOR ASR PROCESSING
        proxy_connect_timeout 60s;
        proxy_send_timeout 300s;           # Extended for large request bodies
        proxy_read_timeout 300s;           # Extended for ASR processing
        send_timeout 300s;

        # 🔧 SSL-SPECIFIC HEADERS
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $server_name;
        proxy_set_header Connection "";

        # 🔧 FORCE HTTP/1.1 FOR BETTER BODY HANDLING
        proxy_http_version 1.1;

        # Backend FastAPI server
        proxy_pass http://localhost:8000;
    }
location /docs/ {
        proxy_pass http://localhost:8000/docs;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }


    location /openapi.json/ {
        proxy_pass http://localhost:8000/openapi.json;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }


    location = / {
        return 200 "Dhruva Platform (HTTPS) - Frontend: /dhruva | Backend API: /backend";
        add_header Content-Type text/plain;
    }
}
