# HTTP server - redirect to HTTPS
server {
    listen 80 default_server;
    listen [::]:80 default_server;
    server_name *************;
    
    # Redirect all HTTP requests to HTTPS
    return 301 https://$server_name$request_uri;
}

# HTTPS server with SSL
server {
    listen 443 ssl default_server;
    listen [::]:443 ssl default_server;
    server_name *************;

    # SSL Configuration
    ssl_certificate /etc/nginx/ssl/dhruva.crt;
    ssl_certificate_key /etc/nginx/ssl/dhruva.key;

    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:50m;
    ssl_session_timeout 10m;

    # 🚨 CRITICAL SSL BUFFER SETTINGS FOR LARGE PAYLOADS - ALIGNED SIZES
    ssl_buffer_size 64k;                   # Aligned with proxy buffers for large payloads

    # 🚨 GLOBAL SETTINGS FOR LARGE REQUEST BODIES OVER SSL
    client_max_body_size 500M;             # Increased for comprehensive ASR testing
    client_body_buffer_size 64k;           # Aligned with SSL buffer size
    client_body_timeout 600s;              # Extended timeout for very large ASR uploads
    client_header_timeout 60s;             # Header timeout

    # 🚨 USE NGINX DEFAULT TEMP PATHS - REMOVE CONFLICTING CUSTOM PATH
    # client_body_temp_path uses nginx default: /var/lib/nginx/body

    # 🚨 CRITICAL: SSL-OPTIMIZED FRONTEND CONFIGURATION FOR LARGE ASR PAYLOADS
    location /dhruva {
        # 🔧 OPTIMIZED REQUEST BUFFERING FOR LARGE ASR PAYLOADS VIA FRONTEND
        proxy_request_buffering on;         # Enable for better large body handling

        # 🔧 SSL-SPECIFIC CLIENT BODY SETTINGS - MATCH BACKEND CONFIGURATION
        client_max_body_size 500M;          # Match global setting for comprehensive ASR
        client_body_buffer_size 64k;        # Aligned with SSL buffer size
        client_body_timeout 600s;           # Extended for very large ASR uploads
        client_body_in_file_only clean;     # Write large bodies to temp files - CRITICAL FOR SSL

        # 🔧 OPTIMIZED PROXY BUFFERING FOR SSL + LARGE PAYLOADS
        proxy_buffering on;                 # Enable for better large response handling
        proxy_buffer_size 64k;              # Aligned with SSL buffer size
        proxy_buffers 16 64k;               # More buffers for large responses
        proxy_busy_buffers_size 128k;       # 2x buffer size
        proxy_temp_file_write_size 64k;     # Aligned with buffer size
        proxy_max_temp_file_size 4096m;     # Increased for very large ASR responses

        # 🔧 SSL-OPTIMIZED PROXY TEMP PATH
        proxy_temp_path /var/lib/nginx/proxy 1 2;  # Use nginx default with subdirs

        # 🔧 EXTENDED TIMEOUTS FOR FRONTEND-BACKEND ASR CHAIN
        proxy_connect_timeout 60s;
        proxy_send_timeout 600s;            # Extended for very large request bodies
        proxy_read_timeout 600s;            # Extended for comprehensive ASR processing
        send_timeout 600s;

        # 🔧 CONDITIONAL CONNECTION HANDLING - WEBSOCKET + LARGE REQUESTS
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $server_name;

        # 🔧 SMART CONNECTION MANAGEMENT FOR BOTH WEBSOCKET AND LARGE REQUESTS
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';

        # 🔧 HTTP/1.1 WITH PROPER CONNECTION MANAGEMENT
        proxy_http_version 1.1;
        proxy_cache_bypass $http_upgrade;

        # Frontend Next.js server
        proxy_pass http://localhost:3001;
    }

    # Backend API - FastAPI on port 8000 under /backend path
location /auth/ {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

        # Backend API - FastAPI on port 8000 under /backend path
# location /services/ {
#         proxy_pass http://localhost:8000;
#         client_max_body_size 20M;
#         proxy_http_version 1.1;
#         proxy_set_header Host $host;
#         proxy_set_header X-Real-IP $remote_addr;
#         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#         proxy_set_header X-Forwarded-Proto $scheme;
#     }

    # 🚨 CRITICAL: SSL-OPTIMIZED ASR SERVICES CONFIGURATION
    location /services/ {
        # 🔧 OPTIMIZED REQUEST BUFFERING FOR LARGE ASR PAYLOADS
        proxy_request_buffering on;         # Enable for better large body handling

        # 🔧 SSL-SPECIFIC CLIENT BODY SETTINGS - ALIGNED WITH GLOBAL
        client_max_body_size 500M;          # Match global setting for comprehensive ASR
        client_body_buffer_size 64k;        # Aligned with SSL buffer size
        client_body_timeout 600s;           # Extended for very large ASR uploads
        client_body_in_file_only clean;     # Write large bodies to temp files - CRITICAL FOR SSL

        # 🔧 OPTIMIZED PROXY BUFFERING FOR SSL + LARGE PAYLOADS
        proxy_buffering on;                 # Enable for better large response handling
        proxy_buffer_size 64k;              # Aligned with SSL buffer size
        proxy_buffers 16 64k;               # More buffers for large responses
        proxy_busy_buffers_size 128k;       # 2x buffer size
        proxy_temp_file_write_size 64k;     # Aligned with buffer size
        proxy_max_temp_file_size 4096m;     # Increased for very large ASR responses

        # 🔧 SSL-OPTIMIZED PROXY TEMP PATH
        proxy_temp_path /var/lib/nginx/proxy 1 2;  # Use nginx default with subdirs

        # 🔧 EXTENDED TIMEOUTS FOR COMPREHENSIVE ASR PROCESSING
        proxy_connect_timeout 60s;
        proxy_send_timeout 600s;            # Extended for very large request bodies
        proxy_read_timeout 600s;            # Extended for comprehensive ASR processing
        send_timeout 600s;

        # 🔧 SSL-SPECIFIC HEADERS - FIXED HTTP/1.1 CONNECTION HANDLING
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $server_name;
        proxy_set_header Connection "keep-alive";  # Proper HTTP/1.1 connection handling

        # 🔧 HTTP/1.1 WITH PROPER CONNECTION MANAGEMENT
        proxy_http_version 1.1;

        # Backend FastAPI server
        proxy_pass http://localhost:8000;
    }
location /docs/ {
        proxy_pass http://localhost:8000/docs;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }


    location /openapi.json/ {
        proxy_pass http://localhost:8000/openapi.json;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }


    location = / {
        return 200 "Dhruva Platform (HTTPS) - Frontend: /dhruva | Backend API: /backend";
        add_header Content-Type text/plain;
    }
}
