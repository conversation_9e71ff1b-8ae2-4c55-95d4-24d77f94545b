##
# Corrected Nginx Configuration for Dhruva Platform Next.js Frontend
# This configuration properly handles Next.js static assets and API routes
##

# Default server configuration
server {
        listen 80 default_server;
        listen [::]:80 default_server;

        server_name _;

        # Security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;

        # Next.js static assets - highest priority for performance
        # Main Next.js application - catch all other routes
        location /dhruva {
                proxy_pass http://localhost:3001;
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection 'upgrade';
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_cache_bypass $http_upgrade;
                
                # Support for WebSocket connections (if needed for real-time features)
                proxy_read_timeout 86400;
        }

        # Error pages
        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;
}
